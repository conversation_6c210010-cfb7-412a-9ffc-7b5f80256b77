import assert from "node:assert";
import http from "node:http";
import { TLSSocket } from "node:tls";
import { dehydrate, QueryClient } from "@tanstack/react-query";
import { type ExecutionResult, print } from "graphql";
import isBot from "isbot";
import * as React from "react";
import * as ReactDom from "react-dom/server";
import UrlPattern from "url-pattern";

import App from "./_app.js";
import { createEmptyHeadState } from "./components/Head.js";
import { GraphQlContext, yogaInstance } from "./dependencies.js";
import { type GraphQlFetcher, graphql } from "./graphql/client.js";
import { GET as grapqhl__GET, POST as grapqhl__POST } from "./pages/graphql.js";
import { GET as img__filename__GET } from "./pages/img/[filename].js";
import { POST as invite__POST } from "./pages/invite.js";
import { POST as generate_authentication_options__POST } from "./pages/webauthn/generate_authentication_options.js";
import { POST as generate_registration_options__POST } from "./pages/webauthn/generate_registration_options.js";
import { POST as verify_authentication__POST } from "./pages/webauthn/verify_authentication.js";
import { POST as verify_registration__POST } from "./pages/webauthn/verify_registration.js";

export default async function handleRequest(
  req: http.IncomingMessage,
  res: http.ServerResponse,
  params: { indexHtml: string; errorHtml: string },
): Promise<void> {
  try {
    const routes: [string, string, RequestHandler][] = [
      ["GET", "/graphql", grapqhl__GET],
      ["POST", "/graphql", grapqhl__POST],
      ["POST", "/invite", invite__POST],
      [
        "POST",
        "/webauthn/generate_authentication_options",
        generate_authentication_options__POST,
      ],
      [
        "POST",
        "/webauthn/generate_registration_options",
        generate_registration_options__POST,
      ],
      ["POST", "/webauthn/verify_authentication", verify_authentication__POST],
      ["POST", "/webauthn/verify_registration", verify_registration__POST],
      ["GET", "/img/:filename.jpg", img__filename__GET],
      ["*", "*", createRenderer(params.indexHtml, params.errorHtml)],
    ];

    const method = req.method ?? "GET";
    const pathname = new URL(req.url ?? "/", getRemoteHost(req)).pathname;
    const route = routes.find(([routeMethod, routePattern]) => {
      return (
        (routeMethod === "*" || routeMethod === method) &&
        (routePattern === "*" || new UrlPattern(routePattern).match(pathname))
      );
    });

    assert(route, `Unhandled route: ${method} ${pathname}`);

    const handler = route[2];

    return await handler(req, res);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(error);
    res.statusCode = 500;
    res.end();
  }

  return undefined;
}

function createRenderer(indexHtml: string, errorHtml: string): RequestHandler {
  return async function render(
    req: http.IncomingMessage,
    res: http.ServerResponse,
  ): Promise<void> {
    const [firstStaticPart, lastStaticPart] =
      indexHtml.split("<!--ssr-outlet-->");
    const [secondStaticPart, thirdStaticPart] = lastStaticPart.split(
      "<!--react-query-state-->",
    );
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          suspense: true,
        },
      },
    });

    const initialURL = new URL(req.url ?? "/", getRemoteHost(req));
    const fetcher = createGraphQlFetcher(req, res, getLocalHost(req));
    const shouldUseSEO = req.headers["user-agent"]
      ? isBot(req.headers["user-agent"])
      : false;
    const routerState: RouterState = { url: initialURL };

    // Pre-fetch random movie data for Header component
    const randomOffset = Math.floor(Math.random() * 1000);
    await queryClient.prefetchQuery({
      queryKey: ["GetRandomTopMovie", { offset: randomOffset }],
      queryFn: () => fetcher(GetRandomTopMovie, { offset: randomOffset }),
    });

    let dehydratedState: unknown;
    const headState = createEmptyHeadState();

    if (shouldUseSEO) {
      try {
        await new Promise<void>((resolve, reject) => {
          ReactDom.renderToPipeableStream(
            React.createElement(App, {
              initialURL,
              onURLChange: (url, match) => {
                routerState.url = url;
                routerState.notFound = match.pattern === "*";
              },
              fetcher,
              queryClient,
              headState,
            }),
            {
              onAllReady() {
                dehydratedState = dehydrate(queryClient);

                resolve();
              },
              onError(error) {
                reject(error);
              },
            },
          );
        });

        if (
          headState.canonical &&
          headState.canonical !== initialURL.toString()
        ) {
          // eslint-disable-next-line require-atomic-updates
          res.statusCode = 308; /* 308 Permanent Redirect */
          res.setHeader("Location", headState.canonical);
          res.end();

          return;
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error);
      }
    }

    let didError = false;
    const didNotFound = Boolean(routerState.notFound);
    const reactStream = ReactDom.renderToPipeableStream(
      React.createElement(App, {
        initialURL: routerState.url,
        fetcher,
        queryClient,
        dehydratedState,
      }),
      {
        onShellReady() {
          res.statusCode = didError ? 500 : didNotFound ? 404 : 200;
          res.setHeader("Content-Type", "text/html");
          res.write(
            firstStaticPart.replace(
              "<!--metadata-outlet-->",
              ReactDom.renderToString(headState.headFragment),
            ),
          );
          reactStream.pipe(res);
        },
        onShellError() {
          res.statusCode = 500;
          res.setHeader("Content-Type", "text/html");
          res.write(errorHtml);
          res.end();
        },
        onAllReady() {
          if (didError) {
            return;
          }

          res.write(secondStaticPart);
          res.write(
            `<script>
              window.__REACT_QUERY_STATE__ = ${JSON.stringify(
                dehydrate(queryClient),
              )};
              window.__ROUTER_STATE__ = ${JSON.stringify(routerState)};
            </script>`,
          );
          res.write(thirdStaticPart);
          res.end();
        },
        onError(error) {
          // eslint-disable-next-line no-console
          console.error(error);
          didError = true;
        },
      },
    );
  };
}

function getRemoteHost(req: http.IncomingMessage): URL {
  const protocol =
    req.socket instanceof TLSSocket
      ? "https"
      : req.headers["x-forwarded-proto"] ?? "http";
  const host = req.headers["x-forwarded-host"] ?? req.headers.host!;

  return new URL(protocol + "://" + host);
}

function getLocalHost(req: http.IncomingMessage): URL {
  const protocol = "http";
  const host = req.headers.host!;

  return new URL(protocol + "://" + host);
}

function createGraphQlFetcher(
  req: http.IncomingMessage,
  res: http.ServerResponse,
  hostUrl: URL,
): GraphQlFetcher {
  return async function serverFetcher(document, variables) {
    const context: GraphQlContext = { req, res, ssr: true };
    const response = await yogaInstance.fetch(
      new Request(new URL("/graphql", hostUrl), {
        method: "POST",
        headers: {
          ...(req.headers as HeadersInit),
          "content-type": "application/json",
        },
        body: JSON.stringify({
          query: print(document),
          variables,
        }),
      }),
      context,
    );

    const json = (await response.json()) as ExecutionResult<unknown>;

    if (json.errors) {
      const { message } = json.errors[0];

      throw new Error(message);
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-explicit-any
    return json.data as any;
  };
}

interface RequestHandler {
  (req: http.IncomingMessage, res: http.ServerResponse): Promise<void>;
}

const GetRandomTopMovie = graphql(/* GraphQL */ `
  query GetRandomTopMovie($offset: Int!) {
    movies(sort: BEST_FIRST, offset: $offset, limit: 1) {
      id
      title
      topPositionAllTime
      images {
        sizes {
          height
          url
          width
        }
      }
    }
  }
`);

interface RouterState {
  url: URL;
  notFound?: boolean;
}
